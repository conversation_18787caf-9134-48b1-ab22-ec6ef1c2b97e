\section{Overview of SAGIN Architectures and Components}

Space-Air-Ground Integrated Networks (SAGIN) represent a transformative architecture in wireless communications, integrating satellite-based space networks, aerial platforms, and terrestrial ground systems to achieve ubiquitous, high-performance connectivity \cite{liu2018space}. This heterogeneous framework addresses the limitations of traditional terrestrial networks by providing global coverage, enhanced reliability, and support for diverse applications such as remote sensing, emergency response, and broadband access in underserved areas.
<argument name="citation_id">24</argument>
 SAGIN is particularly pivotal for the evolution toward 6G, where seamless integration across domains is essential for meeting stringent requirements like ultra-low latency and massive connectivity.
<argument name="citation_id">30</argument>


\subsection{Layered Architecture and Inter-Domain Interfaces}

The SAGIN architecture is typically organized into a multi-layered structure encompassing physical, data link, network, and application layers, with emphasis on inter-domain interfaces to facilitate coordination among space, air, and ground segments.
<argument name="citation_id">22</argument>
 At the physical layer, SAGIN leverages diverse transmission media, including radio frequency (RF) for ground-air links, free-space optical (FSO) for air-space communications, and millimeter-wave (mmWave) bands for high-throughput satellite services.
<argument name="citation_id">23</argument>
 The data link layer handles medium access control (MAC) and error correction, adapting to dynamic topologies through protocols like adaptive modulation and coding.
<argument name="citation_id">21</argument>


The network layer focuses on routing and resource allocation across domains, often employing hierarchical SDN controllers for centralized management.
<argument name="citation_id">26</argument>
 Inter-domain interfaces, such as satellite-to-air handovers and ground-to-satellite backhauls, are critical for maintaining connectivity, with standards defining protocols for seamless integration.
<argument name="citation_id">35</argument>
 The application layer supports service-oriented orchestration, enabling customized resource provisioning for scenarios like disaster management.
<argument name="citation_id">24</argument>


Four primary SAGIN architectures are identified: hybrid satellite-terrestrial relay, satellite-terrestrial backhaul, cognitive satellite-terrestrial, and cooperative satellite-terrestrial networks, each tailored to specific operational needs.
<argument name="citation_id">19</argument>


\subsection{Key Components}

SAGIN comprises three core segments:

\begin{enumerate}
    \item \textbf{Space Segment}: This includes satellites in geostationary Earth orbit (GEO), medium Earth orbit (MEO), and low Earth orbit (LEO). LEO constellations, such as those with inter-satellite links (ISL) and inter-layer links (ILL), offer low-latency global coverage, with GEO providing wide-area broadcasting and MEO balancing coverage and delay.
<argument name="citation_id">19</argument>
 Ground infrastructure, including base stations and control centers, supports satellite operations.
    
    \item \textbf{Air Segment}: High-altitude platforms (HAPs), such as stratospheric balloons and aircraft operating at 17--22 km, provide regional coverage for broadcasting and emergency services.
<argument name="citation_id">20</argument>
 Low-altitude platforms (LAPs), primarily unmanned aerial vehicles (UAVs), offer flexible, on-demand relaying with improved endurance through advanced batteries and charging technologies.
<argument name="citation_id">21</argument>
 UAV swarms enhance survivability in dynamic environments.
    
    \item \textbf{Ground Segment}: Terrestrial components include 5G/6G base stations, mobile ad hoc networks (MANETs), and wireless local area networks (WLANs), integrated via SDN/NFV for efficient resource management.
<argument name="citation_id">22</argument>

<argument name="citation_id">26</argument>

\end{enumerate}

\subsection{Standardization Efforts}

Standardization is crucial for SAGIN interoperability. The 3rd Generation Partnership Project (3GPP) has integrated non-terrestrial networks (NTN) into 5G through Release 17 (Rel-17), enabling satellite-terrestrial convergence for ubiquitous coverage.
<argument name="citation_id">0</argument>

<argument name="citation_id">1</argument>
 This includes support for fixed and non-terrestrial access, expanding from Rel-15/16's focus on terrestrial enhancements.
<argument name="citation_id">2</argument>
 The International Telecommunication Union (ITU) regulates frequency allocations, such as L-band for satellite-ground links and mmWave bands (Ku, Ka, Q/V, W) for high-throughput services, considering interference and environmental factors.
<argument name="citation_id">39</argument>
 Ongoing ITU-R efforts align with 6G visions, including IMT-2030, to standardize SAGIN for global deployment.
<argument name="citation_id">40</argument>


\subsection{Enabling Technologies}

Software-defined networking (SDN) and network function virtualization (NFV) are foundational to SAGIN.
<argument name="citation_id">37</argument>

<argument name="citation_id">58</argument>
 SDN separates control and data planes, enabling dynamic resource orchestration and mobility management through centralized controllers.
<argument name="citation_id">22</argument>
 NFV virtualizes functions for flexible deployment, reducing costs and supporting network slicing for service isolation.
<argument name="citation_id">58</argument>
 These technologies facilitate multi-domain frameworks, such as AI-based service function chain orchestration (AI-SFCO), for handling heterogeneous resources.
<argument name="citation_id">20</argument>
 Other enablers include edge computing for low-latency processing and blockchain for secure data handling.
<argument name="citation_id">28</argument>


\subsection{Comparative Analysis with Traditional Networks}

Table~\ref{tab:comparison} presents a comparison of SAGIN with traditional 5G/6G terrestrial networks across key metrics.

\begin{table*}[t]
\centering
\caption{Comparison of SAGIN with 5G and 6G Terrestrial Networks}
\label{tab:comparison}
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Metric} & \textbf{SAGIN} & \textbf{5G Terrestrial} & \textbf{6G Terrestrial (Projected)} \\
\hline
Latency & Low (LEO: $\sim$20-50 ms) & Ultra-low ($\sim$1 ms) & Extreme-low ($<$1 ms) \\
Bandwidth & High (multi-beam satellites) & eMBB (up to 20 Gbps) & feMBB ($>$100 Gbps) \\
Energy Efficiency & Moderate (edge optimization) & High (mMTC focus) & Extreme-low power \\
Coverage & Global, 3D & Urban/rural, 2D & Enhanced, but limited NTN \\
\hline
\end{tabular}
\end{table*}

SAGIN excels in global three-dimensional coverage, surpassing 5G's terrestrial focus by integrating NTN for remote areas, though it incurs higher latency in GEO links compared to 5G's ultra-reliable low-latency communications (uRLLC).
<argument name="citation_id">49</argument>

<argument name="citation_id">50</argument>
 For 6G, SAGIN extends enhanced mobile broadband (eMBB) to further eMBB (feMBB) with higher data rates, while improving energy efficiency through cloud-edge synergy, addressing 5G's limitations in power-constrained UAVs.
<argument name="citation_id">53</argument>

<argument name="citation_id">54</argument>


\subsection{Recent Deployments}

Recent implementations highlight SAGIN's practical viability. SpaceX's Starlink LEO constellation, with over 8,000 satellites as of 2025, provides low-latency broadband and has integrated UAVs for enhanced operations.
<argument name="citation_id">17</argument>

<argument name="citation_id">9</argument>
 Projects like Amazon's Kuiper aim for services in 2025, competing with Starlink for global coverage.
<argument name="citation_id">18</argument>
 UAV-assisted SAGIN deployments, such as in disaster relief, demonstrate improved routing and resource management.
<argument name="citation_id">21</argument>

<argument name="citation_id">24</argument>
 The integration of 5G core networks on satellites marks progress in NTN.
<argument name="citation_id">3</argument>
