\section{Overview of SAGIN Architectures and Components}

Space-Air-Ground Integrated Networks (SAGIN) represent a transformative architecture in wireless communications, integrating satellite-based space networks, aerial platforms, and terrestrial ground systems to achieve ubiquitous, high-performance connectivity \cite{liu2018space}. This heterogeneous framework addresses the limitations of traditional terrestrial networks by providing global coverage, enhanced reliability, and support for diverse applications such as remote sensing, emergency response, and broadband access in underserved areas \cite{chen2022sagin}. SAGIN is particularly pivotal for the evolution toward 6G, where seamless integration across domains is essential for meeting stringent requirements like ultra-low latency and massive connectivity \cite{zhang20236g}.


\subsection{Layered Architecture and Inter-Domain Interfaces}

The SAGIN architecture is typically organized into a multi-layered structure encompassing physical, data link, network, and application layers, with emphasis on inter-domain interfaces to facilitate coordination among space, air, and ground segments \cite{wang2022layered}. At the physical layer, SAGIN leverages diverse transmission media, including radio frequency (RF) for ground-air links, free-space optical (FSO) for air-space communications, and millimeter-wave (mmWave) bands for high-throughput satellite services \cite{li2023physical}. The data link layer handles medium access control (MAC) and error correction, adapting to dynamic topologies through protocols like adaptive modulation and coding \cite{kumar2022adaptive}.


The network layer focuses on routing and resource allocation across domains, often employing hierarchical SDN controllers for centralized management \cite{zhao2023sdn}. Inter-domain interfaces, such as satellite-to-air handovers and ground-to-satellite backhauls, are critical for maintaining connectivity, with standards defining protocols for seamless integration \cite{ahmed2022handover}. The application layer supports service-oriented orchestration, enabling customized resource provisioning for scenarios like disaster management \cite{chen2022sagin}.

Four primary SAGIN architectures are identified: hybrid satellite-terrestrial relay, satellite-terrestrial backhaul, cognitive satellite-terrestrial, and cooperative satellite-terrestrial networks, each tailored to specific operational needs \cite{tang2021architectures}.


\subsection{Key Components}

SAGIN comprises three core segments:

\begin{enumerate}
    \item \textbf{Space Segment}: This includes satellites in geostationary Earth orbit (GEO), medium Earth orbit (MEO), and low Earth orbit (LEO). LEO constellations, such as those with inter-satellite links (ISL) and inter-layer links (ILL), offer low-latency global coverage, with GEO providing wide-area broadcasting and MEO balancing coverage and delay \cite{tang2021architectures}. Ground infrastructure, including base stations and control centers, supports satellite operations.

    \item \textbf{Air Segment}: High-altitude platforms (HAPs), such as stratospheric balloons and aircraft operating at 17--22 km, provide regional coverage for broadcasting and emergency services \cite{yang2022haps}. Low-altitude platforms (LAPs), primarily unmanned aerial vehicles (UAVs), offer flexible, on-demand relaying with improved endurance through advanced batteries and charging technologies \cite{kumar2022adaptive}. UAV swarms enhance survivability in dynamic environments.

    \item \textbf{Ground Segment}: Terrestrial components include 5G/6G base stations, mobile ad hoc networks (MANETs), and wireless local area networks (WLANs), integrated via SDN/NFV for efficient resource management \cite{wang2022layered,zhao2023sdn}.

\end{enumerate}

\subsection{Standardization Efforts}

Standardization is crucial for SAGIN interoperability. The 3rd Generation Partnership Project (3GPP) has integrated non-terrestrial networks (NTN) into 5G through Release 17 (Rel-17), enabling satellite-terrestrial convergence for ubiquitous coverage \cite{3gpp2022rel17,3gpp2023ntn}. This includes support for fixed and non-terrestrial access, expanding from Rel-15/16's focus on terrestrial enhancements \cite{3gpp2021enhancement}. The International Telecommunication Union (ITU) regulates frequency allocations, such as L-band for satellite-ground links and mmWave bands (Ku, Ka, Q/V, W) for high-throughput services, considering interference and environmental factors \cite{itu2023frequency}. Ongoing ITU-R efforts align with 6G visions, including IMT-2030, to standardize SAGIN for global deployment \cite{itu2023imt2030}.


\subsection{Enabling Technologies}

Software-defined networking (SDN) and network function virtualization (NFV) are foundational to SAGIN \cite{kim2023sdn,rodriguez2022nfv}. SDN separates control and data planes, enabling dynamic resource orchestration and mobility management through centralized controllers \cite{wang2022layered}. NFV virtualizes functions for flexible deployment, reducing costs and supporting network slicing for service isolation \cite{rodriguez2022nfv}. These technologies facilitate multi-domain frameworks, such as AI-based service function chain orchestration (AI-SFCO), for handling heterogeneous resources \cite{yang2022haps}. Other enablers include edge computing for low-latency processing and blockchain for secure data handling \cite{patel2023edge}.


\subsection{Comparative Analysis with Traditional Networks}

Table~\ref{tab:comparison} presents a comparison of SAGIN with traditional 5G/6G terrestrial networks across key metrics.

\begin{table*}[t]
\centering
\caption{Comparison of SAGIN with 5G and 6G Terrestrial Networks}
\label{tab:comparison}
\begin{tabular}{|l|c|c|c|}
\hline
\textbf{Metric} & \textbf{SAGIN} & \textbf{5G Terrestrial} & \textbf{6G Terrestrial (Projected)} \\
\hline
Latency & Low (LEO: $\sim$20-50 ms) & Ultra-low ($\sim$1 ms) & Extreme-low ($<$1 ms) \\
Bandwidth & High (multi-beam satellites) & eMBB (up to 20 Gbps) & feMBB ($>$100 Gbps) \\
Energy Efficiency & Moderate (edge optimization) & High (mMTC focus) & Extreme-low power \\
Coverage & Global, 3D & Urban/rural, 2D & Enhanced, but limited NTN \\
\hline
\end{tabular}
\end{table*}

SAGIN excels in global three-dimensional coverage, surpassing 5G's terrestrial focus by integrating NTN for remote areas, though it incurs higher latency in GEO links compared to 5G's ultra-reliable low-latency communications (uRLLC) \cite{johnson2023coverage,smith2022latency}. For 6G, SAGIN extends enhanced mobile broadband (eMBB) to further eMBB (feMBB) with higher data rates, while improving energy efficiency through cloud-edge synergy, addressing 5G's limitations in power-constrained UAVs \cite{brown2023energy,davis2022efficiency}.


\subsection{Recent Deployments}

Recent implementations highlight SAGIN's practical viability. SpaceX's Starlink LEO constellation, with over 8,000 satellites as of 2025, provides low-latency broadband and has integrated UAVs for enhanced operations \cite{starlink2024deployment,musk2023starlink}. Projects like Amazon's Kuiper aim for services in 2025, competing with Starlink for global coverage \cite{amazon2024kuiper}. UAV-assisted SAGIN deployments, such as in disaster relief, demonstrate improved routing and resource management \cite{kumar2022adaptive,chen2022sagin}. The integration of 5G core networks on satellites marks progress in NTN \cite{3gpp2022satellite}.
